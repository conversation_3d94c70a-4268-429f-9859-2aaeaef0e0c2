# Frontend-Backend Authentication Integration Documentation

## Overview

This document describes the complete authentication system integration between the Angular frontend and NestJS backend. The system provides secure user authentication with JWT tokens, role-based access control, and comprehensive error handling.

## Architecture

### Backend (NestJS)
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT tokens with bcrypt password hashing
- **Email**: <PERSON>demailer for password reset emails
- **Validation**: class-validator for input validation

### Frontend (Angular)
- **Framework**: Angular 18+ with TypeScript
- **HTTP Client**: Angular HttpClient with interceptors
- **State Management**: RxJS BehaviorSubject for auth state
- **Storage**: localStorage for token persistence
- **Guards**: Route guards for authentication and authorization

## Authentication Flow

### 1. User Registration
```
Frontend (RegisterComponent) → Backend (/api/auth/register) → Database
```

**Frontend Implementation:**
- Form validation (email format, password length)
- Calls `AuthService.register()`
- Automatically logs in user on successful registration
- Redirects to home page

**Backend Implementation:**
- Validates input data
- Checks for existing email
- Hashes password with bcrypt (12 rounds)
- Creates user in database
- Returns user data and JWT token

### 2. User Login
```
Frontend (LoginComponent) → Backend (/api/auth/login) → Database
```

**Frontend Implementation:**
- Form validation
- Calls `AuthService.login()`
- Stores user data and token in localStorage
- Updates authentication state
- Redirects to intended page or home

**Backend Implementation:**
- Validates credentials
- Compares password with bcrypt
- Generates JWT token (7-day expiry)
- Returns user data and token

### 3. Password Reset Flow
```
Frontend (ForgotPasswordComponent) → Backend (/api/auth/forgot-password) → Email Service
Frontend (ResetPasswordComponent) → Backend (/api/auth/reset-password) → Database
```

**Forgot Password:**
- User enters email
- Backend generates reset token (1-hour expiry)
- Email sent with reset link
- Returns success message regardless of email existence (security)

**Reset Password:**
- User clicks email link with token
- Frontend extracts token from URL
- User enters new password
- Backend validates token and updates password

### 4. Token Management
- **Storage**: localStorage (frontend)
- **Validation**: JWT signature verification (backend)
- **Expiry**: 7 days default
- **Refresh**: Automatic logout on token expiry

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/register` | User registration | No |
| POST | `/api/auth/login` | User login | No |
| POST | `/api/auth/forgot-password` | Request password reset | No |
| POST | `/api/auth/reset-password` | Reset password with token | No |

### User Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/user/profile` | Get user profile | Yes |
| PATCH | `/api/user/profile` | Update user profile | Yes |
| POST | `/api/user/change-password` | Change password | Yes |

### Admin Endpoints

| Method | Endpoint | Description | Auth Required | Role Required |
|--------|----------|-------------|---------------|---------------|
| GET | `/api/admin/users` | List all users | Yes | ADMIN |
| GET | `/api/admin/products` | Manage products | Yes | ADMIN |

## Request/Response Formats

### Registration Request
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",      // Optional
  "lastName": "Doe",        // Optional
  "phone": "+1234567890"    // Optional
}
```

### Registration/Login Response
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+1234567890",
      "role": "CUSTOMER",
      "createdAt": "2025-06-23T07:56:33.728Z",
      "updatedAt": "2025-06-23T07:56:33.728Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Registration successful"
}
```

### Error Response
```json
{
  "success": false,
  "message": "User with this email already exists",
  "statusCode": 409
}
```

## Frontend Components

### AuthService
**Location**: `frontend/src/app/core/services/auth.service.ts`

**Key Methods:**
- `register()`: User registration
- `login()`: User authentication
- `logout()`: Clear auth data and redirect
- `forgotPassword()`: Request password reset
- `resetPassword()`: Reset password with token
- `isLoggedIn()`: Check authentication status
- `isAdmin()`: Check admin role
- `getCurrentUser()`: Get current user data

**State Management:**
- Uses BehaviorSubject for reactive auth state
- Persists data in localStorage
- Validates JWT token expiry

### Route Guards

**AuthGuard** (`frontend/src/app/core/guards/auth.guard.ts`):
- Protects authenticated routes
- Redirects to login if not authenticated
- Preserves intended URL for post-login redirect

**AdminGuard** (`frontend/src/app/core/guards/admin.guard.ts`):
- Protects admin-only routes
- Requires both authentication and ADMIN role
- Redirects based on auth status

### HTTP Interceptor

**AuthInterceptor** (`frontend/src/app/core/interceptors/auth.interceptor.ts`):
- Automatically adds Bearer token to requests
- Handles 401 responses (token expiry)
- Triggers logout and redirect on auth failure

## Security Features

### Password Security
- Minimum 6 characters (frontend validation)
- bcrypt hashing with 12 salt rounds (backend)
- No password storage in plain text

### Token Security
- JWT with HMAC SHA256 signature
- 7-day expiry (configurable)
- Automatic validation on each request
- Secure token storage considerations

### Input Validation
- Email format validation
- Password strength requirements
- SQL injection protection (Prisma ORM)
- XSS protection (input sanitization)

### CORS Configuration
- Configured for frontend domain
- Proper headers for cross-origin requests
- Preflight request handling

## Error Handling

### Frontend Error Handling
- HTTP error interceptor
- User-friendly error messages
- Form validation errors
- Network error handling

### Backend Error Handling
- Global exception filter
- Validation pipe for DTOs
- Proper HTTP status codes
- Detailed error logging

## Testing

### Test Files
- `backend/auth-test.http`: Backend API testing
- `frontend/auth-integration-test.http`: Integration testing
- `frontend/frontend-auth-test.http`: Frontend-specific testing

### Test Coverage
- Registration flow (success/failure cases)
- Login flow (valid/invalid credentials)
- Password reset flow (complete cycle)
- Token validation and expiry
- Role-based access control
- Error scenarios and edge cases

## Deployment Considerations

### Environment Variables
```bash
# Backend (.env)
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d
DATABASE_URL=postgresql://...
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password
```

### Frontend Environment
```typescript
// frontend/src/environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api'
};
```

### Security Checklist
- [ ] Use HTTPS in production
- [ ] Secure JWT secret key
- [ ] Configure CORS properly
- [ ] Implement rate limiting
- [ ] Add request logging
- [ ] Set up monitoring
- [ ] Regular security updates

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check backend CORS configuration
   - Verify frontend URL in allowed origins

2. **Token Expiry**
   - Check JWT_EXPIRES_IN setting
   - Implement token refresh if needed

3. **Login Failures**
   - Verify password hashing consistency
   - Check database connection
   - Validate input data format

4. **Route Protection**
   - Ensure guards are properly configured
   - Check token storage and retrieval
   - Verify role assignments

### Debug Tools
- Browser DevTools Network tab
- Backend console logs
- JWT token decoder (jwt.io)
- Database query logs

## Future Enhancements

### Potential Improvements
- Token refresh mechanism
- Remember me functionality
- Social login integration
- Two-factor authentication
- Session management
- Audit logging
- Password complexity requirements
- Account lockout policies

### Performance Optimizations
- Token caching strategies
- Lazy loading for auth components
- Optimized database queries
- CDN for static assets
