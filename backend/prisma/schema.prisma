generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               Int       @id @default(autoincrement())
  email            String    @unique
  password         String
  firstName        String?
  lastName         String?
  phone            String?
  role             Role      @default(CUSTOMER)
  resetToken       String?
  resetTokenExpiry DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  
  cart Cart?
  
  @@map("users")
}

model Product {
  id               Int        @id @default(autoincrement())
  name             String
  shortDescription String
  description      String?
  price            Float
  imageUrl         String
  images           String[]   @default([])
  sku              String?    @unique
  category         String?
  stock            Int        @default(0)
  isActive         Boolean    @default(true)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  cartItems CartItem[]

  @@map("products")
}

model Cart {
  id        Int      @id @default(autoincrement())
  userId    Int      @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  user  User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items CartItem[]
  
  @@map("carts")
}

model CartItem {
  id       Int @id @default(autoincrement())
  cartId   Int
  productId Int
  quantity Int @default(1)
  addedAt  DateTime @default(now())
  
  cart    Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([cartId, productId])
  @@map("cart_items")
}

enum Role {
  ADMIN
  CUSTOMER
}