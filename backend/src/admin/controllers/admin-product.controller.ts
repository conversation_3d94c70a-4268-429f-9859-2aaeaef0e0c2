import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query,
  ParseIntPipe,
  UseGuards
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody 
} from '@nestjs/swagger';
import { Product, Role } from '@prisma/client';

import { ProductService } from '../../product/product.service';
import { ApiResponse as CustomApiResponse, PaginatedResponse } from '../../common/dto/response.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

import { CreateProductDto } from '../../product/dto/create-product.dto';
import { SearchProductDto } from '../../product/dto/search-product.dto';
import { UpdateProductDto } from '../../product/dto/update-product.dto';

@ApiTags('admin/products')
@Controller('admin/products')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@ApiBearerAuth('JWT-auth')
export class AdminProductController {
  constructor(private readonly productsService: ProductService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Create new product',
    description: 'Create a new product (Admin only)' 
  })
  @ApiBody({ type: CreateProductDto })
  @ApiResponse({
    status: 201,
    description: 'Product created successfully',
  })
  async create(@Body() createProductDto: CreateProductDto): Promise<CustomApiResponse<Product>> {
    const product = await this.productsService.create(createProductDto);
    return {
      success: true,
      data: product,
      message: 'Product created successfully',
    };
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get all products for admin',
    description: 'Retrieve paginated list of all products with admin filters (Admin only)' 
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for product name, description, category, or SKU' })
  @ApiQuery({ name: 'minPrice', required: false, type: Number, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', required: false, type: Number, description: 'Maximum price filter' })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean, description: 'Filter by active status' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  async findAll(@Query() query: SearchProductDto): Promise<PaginatedResponse<Product>> {
    const result = await this.productsService.findAll(query);
    return {
      success: true,
      data: result.products,
      pagination: result.pagination,
      message: 'Products retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get product by ID (Admin)' })
  @ApiParam({ name: 'id', type: String, description: 'Product ID' })
  async findOne(@Param('id') id: string): Promise<CustomApiResponse<Product>> {
    const product = await this.productsService.findOne(id);
    return {
      success: true,
      data: product,
      message: 'Product retrieved successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update product',
    description: 'Update an existing product (Admin only)' 
  })
  @ApiParam({ name: 'id', type: String, description: 'Product ID' })
  @ApiBody({ type: UpdateProductDto })
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
  ): Promise<CustomApiResponse<Product>> {
    const product = await this.productsService.update(id, updateProductDto);
    return {
      success: true,
      data: product,
      message: 'Product updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete product',
    description: 'Delete a product (Admin only). If product is in carts, it will be marked as inactive instead.' 
  })
  @ApiParam({ name: 'id', type: String, description: 'Product ID' })
  async remove(@Param('id') id: string): Promise<CustomApiResponse<Product>> {
    const product = await this.productsService.remove(id);
    const message = product.isActive === false 
      ? 'Product marked as inactive (was in use)' 
      : 'Product deleted successfully';
    
    return {
      success: true,
      data: product,
      message,
    };
  }

  @Patch(':id/stock/increase')
  @ApiOperation({ 
    summary: 'Increase product stock',
    description: 'Increase stock for a product (Admin only)' 
  })
  @ApiParam({ name: 'id', type: Number, description: 'Product ID' })
  @ApiBody({ 
    schema: { 
      type: 'object', 
      properties: { 
        quantity: { type: 'number', minimum: 1 } 
      },
      required: ['quantity']
    } 
  })
  async increaseStock(
    @Param('id') id: string,
    @Body('quantity', ParseIntPipe) quantity: number,
  ): Promise<CustomApiResponse<Product>> {
    const product = await this.productsService.increaseStock(id, quantity);
    return {
      success: true,
      data: product,
      message: `Product stock increased by ${quantity}`,
    };
  }

  @Patch(':id/stock/decrease')
  @ApiOperation({ 
    summary: 'Decrease product stock',
    description: 'Decrease stock for a product (Admin only)' 
  })
  @ApiParam({ name: 'id', type: Number, description: 'Product ID' })
  @ApiBody({ 
    schema: { 
      type: 'object', 
      properties: { 
        quantity: { type: 'number', minimum: 1 } 
      },
      required: ['quantity']
    } 
  })
  async decreaseStock(
    @Param('id') id: string,
    @Body('quantity', ParseIntPipe) quantity: number,
  ): Promise<CustomApiResponse<Product>> {
    const product = await this.productsService.decreaseStock(id, quantity);
    return {
      success: true,
      data: product,
      message: `Product stock decreased by ${quantity}`,
    };
  }

  @Get('stats/overview')
  @ApiOperation({
    summary: 'Get product statistics',
    description: 'Get product statistics (Admin only)'
  })
  async getStats(): Promise<CustomApiResponse<any>> {
    const stats = await this.productsService.getProductStats();
    return {
      success: true,
      data: stats,
      message: 'Product statistics retrieved successfully',
    };
  }

  @Post('generate-sample-data')
  @ApiOperation({
    summary: 'Generate sample products',
    description: 'Generate sample products in the database (Admin only)'
  })
  async generateSampleData(): Promise<CustomApiResponse<Product[]>> {
    const products = await this.productsService.generateSampleProducts();
    return {
      success: true,
      data: products,
      message: `${products.length} sample products generated successfully`,
    };
  }
}
