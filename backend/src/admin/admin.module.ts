import { Module } from '@nestjs/common';
import { AdminProductController } from './controllers/admin-product.controller';
import { AdminUserController } from './controllers/admin-user.controller';
import { ProductModule } from '../product/product.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [ProductModule, UserModule],
  controllers: [AdminProductController, AdminUserController],
})
export class AdminModule {}
