<div style="padding: 0;">
    <!-- <PERSON><PERSON> -->
    <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin-bottom: 24px;">
        <div style="display: flex; align-items: center;">
            <span style="font-size: 20px; margin-right: 8px;">🔒</span>
            <div>
                <h3 style="color: #dc2626; margin: 0; font-size: 16px; font-weight: 600;">Password Reset Request</h3>
                <p style="color: #7f1d1d; margin: 4px 0 0 0; font-size: 14px;">We received a request to reset your password</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div style="margin-bottom: 24px;">
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0 0 16px 0;">
            Hello,
        </p>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0 0 16px 0;">
            You requested a password reset for your <strong><%= platformName %></strong> account associated with <strong><%= userEmail %></strong>.
        </p>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 0 0 24px 0;">
            Use the following token to reset your password through our API:
        </p>
    </div>

    <!-- Reset Token Display -->
    <div style="background-color: #f9fafb; border: 1px solid #d1d5db; border-radius: 8px; padding: 16px; margin-bottom: 24px;">
        <p style="color: #374151; font-size: 14px; margin: 0 0 8px 0; font-weight: 600;">
            Your Password Reset Token:
        </p>
        <p style="background-color: #ffffff; border: 1px solid #d1d5db; border-radius: 4px; padding: 12px; font-family: monospace; font-size: 12px; color: #374151; word-break: break-all; margin: 0;">
            <%= resetToken %>
        </p>
    </div>

    <!-- API Instructions -->
    <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px; padding: 16px; margin-bottom: 24px;">
        <h4 style="color: #0369a1; margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">
            API Usage Instructions:
        </h4>
        <p style="color: #0c4a6e; font-size: 14px; margin: 0 0 8px 0;">
            Send a POST request to: <code>http://localhost:3000/api/auth/reset-password</code>
        </p>
        <p style="color: #0c4a6e; font-size: 14px; margin: 0;">
            With JSON body: <code>{"token": "YOUR_TOKEN", "newPassword": "your_new_password"}</code>
        </p>
    </div>

    <!-- Security Info -->
    <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px;">
        <ul style="color: #7f1d1d; font-size: 14px; margin: 0; padding-left: 20px; line-height: 1.5;">
            <li>This token will expire in <strong>1 hour</strong></li>
            <li>You can only use this token once</li>
            <li>If you didn't request this reset, please ignore this email</li>
        </ul>
    </div>
</div>