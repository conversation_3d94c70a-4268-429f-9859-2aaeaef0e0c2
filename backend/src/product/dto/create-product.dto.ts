import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON>Url, <PERSON><PERSON>nt, Is<PERSON><PERSON>al, IsBoolean, <PERSON><PERSON>ength, Min, <PERSON>Array } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateProductDto {
  @ApiProperty({ example: 'iPhone 14 Pro' })
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ example: 'Latest Apple smartphone with advanced features' })
  @IsString()
  @MinLength(1)
  shortDescription: string;

  @ApiProperty({
    example: 'The iPhone 14 Pro features a stunning Super Retina XDR display, powerful A16 Bionic chip, and advanced camera system with 48MP main camera.',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: 999.99 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price: number;

  @ApiProperty({ example: 'https://example.com/image.jpg' })
  @IsString()
  @IsUrl()
  imageUrl: string;

  @ApiProperty({
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsUrl({}, { each: true })
  images?: string[];

  @ApiProperty({ example: 'IPH14PRO-128-BLK', required: false })
  @IsOptional()
  @IsString()
  sku?: string;

  @ApiProperty({ example: 'Electronics', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ example: 50, default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  stock?: number = 0;

  @ApiProperty({ example: true, default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}