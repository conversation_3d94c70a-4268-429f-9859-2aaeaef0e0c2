import { Product, Prisma } from '@prisma/client';

import { PaginatedResponse } from '../common/dto/response.dto';
import { CreateProductDto } from './dto/create-product.dto';
import { SearchProductDto } from './dto/search-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

export interface ProductSearchResult {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface IProductsService {
  create(createProductDto: CreateProductDto): Promise<Product>;
  findAll(searchDto: SearchProductDto): Promise<ProductSearchResult>;
  findOne(id: string): Promise<Product | null>;
  update(id: string, updateProductDto: UpdateProductDto): Promise<Product>;
  remove(id: string): Promise<Product>;
  decreaseStock(id: string, quantity: number): Promise<Product>;
  increaseStock(id: string, quantity: number): Promise<Product>;
  generateSampleProducts(): Promise<Product[]>;
}