### Admin Authentication and Authorization Tests

@baseUrl = http://localhost:3000/api
@adminEmail = <EMAIL>
@adminPassword = admin123
@customerEmail = <EMAIL>
@customerPassword = customer123

### 1. Admin Login (Valid Credentials)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{adminEmail}}",
  "password": "{{adminPassword}}"
}

### 2. Customer Login (Valid Credentials)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{customerEmail}}",
  "password": "{{customerPassword}}"
}

### 3. Invalid Login (Wrong Password)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{adminEmail}}",
  "password": "wrongpassword"
}

### 4. Invalid Login (Non-existent User)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 5. Invalid Login (Missing Fields)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{adminEmail}}"
}

### Set tokens for testing (replace with actual tokens from login responses)
@adminToken = YOUR_ADMIN_JWT_TOKEN_HERE
@customerToken = YOUR_CUSTOMER_JWT_TOKEN_HERE

### 6. Admin Access to Admin Endpoint (Should Succeed)
GET {{baseUrl}}/admin/products
Authorization: Bearer {{adminToken}}

### 7. Customer Access to Admin Endpoint (Should Fail - 403 Forbidden)
GET {{baseUrl}}/admin/products
Authorization: Bearer {{customerToken}}

### 8. No Token Access to Admin Endpoint (Should Fail - 401 Unauthorized)
GET {{baseUrl}}/admin/products

### 9. Invalid Token Access to Admin Endpoint (Should Fail - 401 Unauthorized)
GET {{baseUrl}}/admin/products
Authorization: Bearer invalid_token_here

### 10. Expired Token Access (Should Fail - 401 Unauthorized)
### Note: You'll need to wait for token expiration or use an expired token
GET {{baseUrl}}/admin/products
Authorization: Bearer expired_token_here

### 11. Malformed Token Access (Should Fail - 401 Unauthorized)
GET {{baseUrl}}/admin/products
Authorization: Bearer malformed.token.here

### 12. Admin Create Product (Should Succeed)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Admin Created Product",
  "shortDescription": "Product created by admin user",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "sku": "ADMIN-PROD-001",
  "category": "Test",
  "stock": 10,
  "isActive": true
}

### 13. Customer Create Product (Should Fail - 403 Forbidden)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{customerToken}}

{
  "name": "Customer Attempted Product",
  "shortDescription": "Product creation attempt by customer",
  "price": 50.00,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "sku": "CUSTOMER-PROD-001",
  "category": "Test",
  "stock": 5,
  "isActive": true
}

### 14. Admin Update Product (Should Succeed)
PATCH {{baseUrl}}/admin/products/1
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Updated by Admin",
  "price": 149.99
}

### 15. Customer Update Product (Should Fail - 403 Forbidden)
PATCH {{baseUrl}}/admin/products/1
Content-Type: application/json
Authorization: Bearer {{customerToken}}

{
  "name": "Updated by Customer",
  "price": 199.99
}

### 16. Admin Delete Product (Should Succeed)
DELETE {{baseUrl}}/admin/products/1
Authorization: Bearer {{adminToken}}

### 17. Customer Delete Product (Should Fail - 403 Forbidden)
DELETE {{baseUrl}}/admin/products/1
Authorization: Bearer {{customerToken}}

### 18. Admin Access Product Stats (Should Succeed)
GET {{baseUrl}}/admin/products/stats/overview
Authorization: Bearer {{adminToken}}

### 19. Customer Access Product Stats (Should Fail - 403 Forbidden)
GET {{baseUrl}}/admin/products/stats/overview
Authorization: Bearer {{customerToken}}

### 20. Admin Generate Sample Data (Should Succeed)
POST {{baseUrl}}/admin/products/generate-sample-data
Authorization: Bearer {{adminToken}}

### 21. Customer Generate Sample Data (Should Fail - 403 Forbidden)
POST {{baseUrl}}/admin/products/generate-sample-data
Authorization: Bearer {{customerToken}}

### 22. Admin Stock Management - Increase (Should Succeed)
PATCH {{baseUrl}}/admin/products/1/stock/increase
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": 10
}

### 23. Customer Stock Management - Increase (Should Fail - 403 Forbidden)
PATCH {{baseUrl}}/admin/products/1/stock/increase
Content-Type: application/json
Authorization: Bearer {{customerToken}}

{
  "quantity": 10
}

### 24. Admin Stock Management - Decrease (Should Succeed)
PATCH {{baseUrl}}/admin/products/1/stock/decrease
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": 5
}

### 25. Customer Stock Management - Decrease (Should Fail - 403 Forbidden)
PATCH {{baseUrl}}/admin/products/1/stock/decrease
Content-Type: application/json
Authorization: Bearer {{customerToken}}

{
  "quantity": 5
}

### 26. Test Token Refresh (if implemented)
POST {{baseUrl}}/auth/refresh
Content-Type: application/json
Authorization: Bearer {{adminToken}}

### 27. Test Admin Logout
POST {{baseUrl}}/auth/logout
Authorization: Bearer {{adminToken}}

### 28. Test Customer Logout
POST {{baseUrl}}/auth/logout
Authorization: Bearer {{customerToken}}

### 29. Access After Logout (Should Fail - 401 Unauthorized)
GET {{baseUrl}}/admin/products
Authorization: Bearer {{adminToken}}

### 30. Register New Admin User (if endpoint exists)
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "newadmin123",
  "role": "ADMIN"
}

### 31. Register New Customer User
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "customer123",
  "firstName": "New",
  "lastName": "Customer"
}

### 32. Test Case Sensitivity in Email
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "{{adminPassword}}"
}

### 33. Test SQL Injection Attempt
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>'; DROP TABLE users; --",
  "password": "{{adminPassword}}"
}

### 34. Test XSS Attempt in Login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<script>alert('xss')</script>",
  "password": "{{adminPassword}}"
}

### 35. Test Very Long Password
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{adminEmail}}",
  "password": "verylongpasswordthatexceedsnormallimitsandmightcauseissuesifnothandledproperly123456789"
}
