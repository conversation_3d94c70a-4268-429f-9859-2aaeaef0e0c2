### Admin Product API Edge Cases and <PERSON><PERSON><PERSON>

@baseUrl = http://localhost:3000/api
@adminToken = YOUR_ADMIN_JWT_TOKEN_HERE

### 1. Create Product with Empty Name (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "",
  "shortDescription": "Product with empty name",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 2. Create Product with Very Long Name (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "This is an extremely long product name that exceeds the maximum allowed length for product names in the database and should cause a validation error when attempting to create the product",
  "shortDescription": "Product with very long name",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 3. Create Product with Negative Price (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Negative Price Product",
  "shortDescription": "Product with negative price",
  "price": -50.00,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 4. Create Product with Zero Price (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Zero Price Product",
  "shortDescription": "Product with zero price",
  "price": 0,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 5. Create Product with Invalid Image URL (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Invalid Image Product",
  "shortDescription": "Product with invalid image URL",
  "price": 99.99,
  "imageUrl": "not-a-valid-url"
}

### 6. Create Product with Negative Stock (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Negative Stock Product",
  "shortDescription": "Product with negative stock",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "stock": -10
}

### 7. Create Product with Invalid SKU Format (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Invalid SKU Product",
  "shortDescription": "Product with invalid SKU format",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "sku": "invalid sku with spaces and special chars!@#"
}

### 8. Create Product with Very Short Description (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Short Desc Product",
  "shortDescription": "Short",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 9. Create Product with Missing Required Fields (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "description": "Product missing required fields",
  "sku": "MISSING-FIELDS-001"
}

### 10. Create Product with Invalid JSON (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Invalid JSON Product",
  "shortDescription": "Product with invalid JSON"
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 11. Update Non-Existent Product (Should Fail)
PATCH {{baseUrl}}/admin/products/999999
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Updated Non-Existent Product"
}

### 12. Update Product with Invalid ID Format (Should Fail)
PATCH {{baseUrl}}/admin/products/not-a-number
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Updated Product"
}

### 13. Delete Non-Existent Product (Should Fail)
DELETE {{baseUrl}}/admin/products/999999
Authorization: Bearer {{adminToken}}

### 14. Delete Product with Invalid ID Format (Should Fail)
DELETE {{baseUrl}}/admin/products/not-a-number
Authorization: Bearer {{adminToken}}

### 15. Get Product with Invalid ID Format (Should Fail)
GET {{baseUrl}}/admin/products/not-a-number
Authorization: Bearer {{adminToken}}

### 16. Increase Stock with Negative Quantity (Should Fail)
PATCH {{baseUrl}}/admin/products/1/stock/increase
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": -10
}

### 17. Increase Stock with Zero Quantity (Should Fail)
PATCH {{baseUrl}}/admin/products/1/stock/increase
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": 0
}

### 18. Decrease Stock with Negative Quantity (Should Fail)
PATCH {{baseUrl}}/admin/products/1/stock/decrease
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": -5
}

### 19. Decrease Stock More Than Available (Should Fail)
PATCH {{baseUrl}}/admin/products/1/stock/decrease
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": 10000
}

### 20. Stock Operation with Missing Quantity (Should Fail)
PATCH {{baseUrl}}/admin/products/1/stock/increase
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "amount": 10
}

### 21. Stock Operation with Invalid Quantity Type (Should Fail)
PATCH {{baseUrl}}/admin/products/1/stock/increase
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "quantity": "ten"
}

### 22. Create Product with Extremely Large Price (Edge Case)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Expensive Product",
  "shortDescription": "Product with very high price",
  "price": 999999999.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 23. Create Product with Many Decimal Places in Price (Should Round)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Precise Price Product",
  "shortDescription": "Product with many decimal places",
  "price": 99.999999,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 24. Search with Very Long Search Term
GET {{baseUrl}}/admin/products?search=this-is-a-very-long-search-term-that-might-cause-issues-if-not-handled-properly-in-the-database-query
Authorization: Bearer {{adminToken}}

### 25. Search with Special Characters
GET {{baseUrl}}/admin/products?search=!@#$%^&*()_+-=[]{}|;:,.<>?
Authorization: Bearer {{adminToken}}

### 26. Search with SQL Injection Attempt
GET {{baseUrl}}/admin/products?search='; DROP TABLE products; --
Authorization: Bearer {{adminToken}}

### 27. Pagination with Invalid Page Number
GET {{baseUrl}}/admin/products?page=0
Authorization: Bearer {{adminToken}}

### 28. Pagination with Negative Page Number
GET {{baseUrl}}/admin/products?page=-1
Authorization: Bearer {{adminToken}}

### 29. Pagination with Very Large Page Number
GET {{baseUrl}}/admin/products?page=999999
Authorization: Bearer {{adminToken}}

### 30. Pagination with Invalid Limit
GET {{baseUrl}}/admin/products?limit=0
Authorization: Bearer {{adminToken}}

### 31. Pagination with Negative Limit
GET {{baseUrl}}/admin/products?limit=-10
Authorization: Bearer {{adminToken}}

### 32. Pagination with Very Large Limit
GET {{baseUrl}}/admin/products?limit=10000
Authorization: Bearer {{adminToken}}

### 33. Filter with Invalid Price Range
GET {{baseUrl}}/admin/products?minPrice=100&maxPrice=50
Authorization: Bearer {{adminToken}}

### 34. Filter with Negative Prices
GET {{baseUrl}}/admin/products?minPrice=-50&maxPrice=-10
Authorization: Bearer {{adminToken}}

### 35. Filter with Invalid Boolean Value
GET {{baseUrl}}/admin/products?isActive=maybe
Authorization: Bearer {{adminToken}}

### 36. Create Product with XSS Attempt in Name
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "<script>alert('xss')</script>",
  "shortDescription": "Product with XSS attempt in name",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 37. Create Product with HTML in Description
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "HTML Description Product",
  "shortDescription": "Product with HTML in description",
  "description": "<h1>This is HTML</h1><p>With <strong>bold</strong> text</p>",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 38. Create Product with Unicode Characters
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Unicode Product 🚀 ñáéíóú",
  "shortDescription": "Product with unicode characters 中文 العربية",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 39. Test Rate Limiting (Multiple Rapid Requests)
### Run this multiple times quickly to test rate limiting
GET {{baseUrl}}/admin/products
Authorization: Bearer {{adminToken}}

### 40. Test Large Request Body
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "Large Request Product",
  "shortDescription": "Product with large request body",
  "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "images": [
    "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
    "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500",
    "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500"
  ]
}
