### Admin User Management API Tests
### Base URL: http://localhost:3000/api

### Variables
@baseUrl = http://localhost:3000/api
@adminToken = your_admin_jwt_token_here

### 1. Admin Login (Get Token)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}

### 2. Get All Users (Admin Only)
GET {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}

### 3. Get All Users with Pagination
GET {{baseUrl}}/admin/users?page=1&limit=5
Authorization: Bearer {{adminToken}}

### 4. Search Users by Email
GET {{baseUrl}}/admin/users?search=john
Authorization: Bearer {{adminToken}}

### 5. Filter Users by Role
GET {{baseUrl}}/admin/users?role=CUSTOMER
Authorization: Bearer {{adminToken}}

### 6. Get Users with Sorting
GET {{baseUrl}}/admin/users?sortBy=createdAt&sortOrder=desc
Authorization: Bearer {{adminToken}}

### 7. Create New User (Admin Only)
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+1234567890",
  "role": "CUSTOMER"
}

### 8. Create Admin User
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123",
  "firstName": "Jane",
  "lastName": "Admin",
  "role": "ADMIN"
}

### 9. Get User by ID
GET {{baseUrl}}/admin/users/user_id_here
Authorization: Bearer {{adminToken}}

### 10. Update User
PATCH {{baseUrl}}/admin/users/user_id_here
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "firstName": "Updated John",
  "lastName": "Updated Doe",
  "phone": "+9876543210"
}

### 11. Update User Email
PATCH {{baseUrl}}/admin/users/user_id_here
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>"
}

### 12. Update User Role
PATCH {{baseUrl}}/admin/users/user_id_here/role
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "role": "ADMIN"
}

### 13. Get User Statistics
GET {{baseUrl}}/admin/users/stats/overview
Authorization: Bearer {{adminToken}}

### 14. Delete User
DELETE {{baseUrl}}/admin/users/user_id_here
Authorization: Bearer {{adminToken}}

### 15. Test Error Cases

### 15.1. Create User with Existing Email
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Duplicate",
  "lastName": "User"
}

### 15.2. Create User with Invalid Email
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "invalid-email",
  "password": "password123"
}

### 15.3. Create User with Short Password
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123"
}

### 15.4. Get Non-existent User
GET {{baseUrl}}/admin/users/non-existent-id
Authorization: Bearer {{adminToken}}

### 15.5. Update Non-existent User
PATCH {{baseUrl}}/admin/users/non-existent-id
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "firstName": "Test"
}

### 15.6. Delete Non-existent User
DELETE {{baseUrl}}/admin/users/non-existent-id
Authorization: Bearer {{adminToken}}

### 16. Test Unauthorized Access

### 16.1. Access without Token
GET {{baseUrl}}/admin/users

### 16.2. Access with Invalid Token
GET {{baseUrl}}/admin/users
Authorization: Bearer invalid_token

### 16.3. Access with Customer Token (should fail)
GET {{baseUrl}}/admin/users
Authorization: Bearer customer_jwt_token_here

### 17. Bulk Operations Test Data

### 17.1. Create Multiple Test Users
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Customer",
  "lastName": "One",
  "role": "CUSTOMER"
}

###
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Customer",
  "lastName": "Two",
  "role": "CUSTOMER"
}

###
POST {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Customer",
  "lastName": "Three",
  "role": "CUSTOMER"
}

### 18. Advanced Search Tests

### 18.1. Search by First Name
GET {{baseUrl}}/admin/users?search=Customer
Authorization: Bearer {{adminToken}}

### 18.2. Search by Last Name
GET {{baseUrl}}/admin/users?search=One
Authorization: Bearer {{adminToken}}

### 18.3. Complex Query with Multiple Filters
GET {{baseUrl}}/admin/users?search=customer&role=CUSTOMER&sortBy=firstName&sortOrder=asc&page=1&limit=10
Authorization: Bearer {{adminToken}}
