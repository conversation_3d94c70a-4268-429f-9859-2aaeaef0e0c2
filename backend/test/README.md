# Admin Product Management API Tests

This directory contains comprehensive HTTP test files for testing the admin product management system. These tests are designed to be used with REST clients like VS Code REST Client extension, Postman, or similar tools.

## Test Files

### 1. `admin-products.http`
Main test file containing all standard CRUD operations and functionality tests for the admin product management API.

**Test Categories:**
- Authentication and authorization
- Product creation with various data combinations
- Product retrieval with filtering, pagination, and search
- Product updates (full and partial)
- Stock management operations
- Product deletion
- Statistics and analytics
- Sample data generation

### 2. `admin-auth.http`
Focused on authentication and authorization testing to ensure proper security measures.

**Test Categories:**
- Admin and customer login scenarios
- Token validation and expiration
- Role-based access control
- Unauthorized access attempts
- Security vulnerability tests (SQL injection, XSS)

### 3. `admin-products-edge-cases.http`
Edge cases, error scenarios, and boundary testing to ensure robust error handling.

**Test Categories:**
- Invalid input validation
- Boundary value testing
- Error response verification
- Security testing
- Performance edge cases

## Prerequisites

1. **Backend Server Running**: Ensure the NestJS backend server is running on `http://localhost:3000`

2. **Admin User**: Make sure you have an admin user created with the following credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`

3. **Customer User** (for authorization tests): Create a customer user with:
   - Email: `<EMAIL>`
   - Password: `customer123`

4. **REST Client**: Install a REST client extension/tool:
   - **VS Code**: Install "REST Client" extension by Huachao Mao
   - **Postman**: Import the HTTP files or copy requests manually
   - **IntelliJ IDEA**: Built-in HTTP client support

## Setup Instructions

### 1. Environment Setup
Update the variables at the top of each HTTP file if your setup differs:

```http
@baseUrl = http://localhost:3000/api
@adminEmail = <EMAIL>
@adminPassword = admin123
```

### 2. Authentication Token Setup
1. Run the login request in `admin-auth.http` or `admin-products.http`
2. Copy the JWT token from the response
3. Replace `YOUR_ADMIN_JWT_TOKEN_HERE` with the actual token in all test files

### 3. Product ID Setup
Some tests require existing product IDs. Either:
- Create products first using the create endpoints
- Update the `@productId` variable with existing product IDs from your database

## Running Tests

### Using VS Code REST Client

1. Open any `.http` file in VS Code
2. Click "Send Request" above any HTTP request
3. View the response in the right panel
4. Use variables to avoid repeating common values

### Using Postman

1. Create a new collection
2. Copy individual requests from the HTTP files
3. Set up environment variables for base URL and tokens
4. Run requests individually or as a collection

### Test Execution Order

For best results, follow this order:

1. **Authentication Tests** (`admin-auth.http`):
   - Start with admin login to get JWT token
   - Test various authentication scenarios

2. **Basic CRUD Tests** (`admin-products.http`):
   - Create products
   - Read/search products
   - Update products
   - Delete products

3. **Edge Cases** (`admin-products-edge-cases.http`):
   - Test error scenarios
   - Validate input validation
   - Test boundary conditions

## Expected Responses

### Success Responses
- **200 OK**: Successful GET, PATCH operations
- **201 Created**: Successful POST operations
- **204 No Content**: Successful DELETE operations

### Error Responses
- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions (customer trying to access admin endpoints)
- **404 Not Found**: Resource not found
- **409 Conflict**: Duplicate data (e.g., SKU already exists)

### Response Format
All successful responses follow this format:
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully"
}
```

Paginated responses include:
```json
{
  "success": true,
  "data": [ /* array of items */ ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "Products retrieved successfully"
}
```

## Test Data

### Sample Product Data
The tests use realistic product data including:
- Electronics (headphones, laptops, phones)
- Various price ranges ($25 - $1200)
- Different stock levels
- Multiple image URLs from Unsplash
- Proper SKU formats
- Various categories

### Test Categories
- Electronics
- Clothing
- Books
- Home & Garden
- Sports
- Toys
- Beauty
- Automotive
- Health
- Food & Beverages

## Security Testing

The test files include security-focused tests:

1. **Authentication Bypass Attempts**
2. **SQL Injection Prevention**
3. **XSS Attack Prevention**
4. **Role-Based Access Control**
5. **Input Validation**
6. **Rate Limiting** (if implemented)

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check if JWT token is valid and not expired
   - Ensure token is properly formatted in Authorization header

2. **403 Forbidden**
   - Verify user has admin role
   - Check if using customer token for admin endpoints

3. **404 Not Found**
   - Verify product IDs exist in database
   - Check API endpoint URLs

4. **400 Bad Request**
   - Validate request body format
   - Check required fields are present
   - Verify data types match expected format

### Debug Tips

1. **Check Server Logs**: Monitor backend console for detailed error messages
2. **Validate JSON**: Ensure request bodies are valid JSON
3. **Test Incrementally**: Start with simple requests before complex ones
4. **Use Browser DevTools**: For web-based REST clients, check network tab

## Contributing

When adding new tests:

1. Follow the existing naming convention
2. Include both positive and negative test cases
3. Add appropriate comments explaining test purpose
4. Update this README with new test categories
5. Ensure tests are independent and can run in any order

## Notes

- Tests are designed to be idempotent where possible
- Some tests may modify database state (create/update/delete)
- Consider using a test database for running these tests
- JWT tokens expire after a certain time (check backend configuration)
- Rate limiting may affect rapid test execution
