# API Testing Guide

This directory contains HTTP files for testing the Shopie API endpoints using REST clients like VS Code REST Client, Postman, or Insomnia.

## Files

- `admin-auth.http` - Authentication endpoints for admin users
- `admin-products.http` - Product management endpoints (admin only)
- `admin-users.http` - User management endpoints (admin only)
- `admin-products-edge-cases.http` - Edge cases and error scenarios for product management

## Setup

1. Start the backend server:
   ```bash
   cd backend
   npm run start:dev
   ```

2. Create an admin user (if not already created):
   - The application automatically creates an admin user on startup if `ADMIN_EMAIL` and `ADMIN_PASSWORD` environment variables are set
   - Default admin credentials: `<EMAIL>` / `admin123`

3. Update the variables in the HTTP files:
   - Replace `YOUR_JWT_TOKEN_HERE` with actual JWT tokens obtained from login
   - Replace `your_product_uuid_here` and `user_id_here` with actual UUIDs from your database
   - Update `@baseUrl` if your server runs on a different port

## Database Schema Changes

**Important**: This version uses UUID for all model IDs instead of numeric IDs for better security and scalability.

### Migration Required

Before testing, you need to run the database migration to update the schema:

```bash
cd backend
npx prisma migrate dev --name "convert-ids-to-uuid"
npx prisma generate
```

## Usage

### VS Code REST Client

1. Install the "REST Client" extension
2. Open any `.http` file
3. Click "Send Request" above each request
4. View responses in the right panel

### Postman

1. Import the HTTP files or copy the requests manually
2. Set up environment variables for base URL and tokens
3. Execute requests and view responses

## Test Flow

1. **Authentication**: Start with `admin-auth.http` to get JWT tokens
2. **User Management**: Use `admin-users.http` for user CRUD operations
3. **Product Management**: Use `admin-products.http` for product CRUD operations
4. **Edge Cases**: Test error scenarios with `admin-products-edge-cases.http`

## Admin User Management Features

The new user management system includes:

- **CRUD Operations**: Create, read, update, delete users
- **Role Management**: Assign ADMIN or CUSTOMER roles
- **Search & Filter**: Search by email/name, filter by role
- **Pagination**: Configurable page size and navigation
- **Statistics**: User count and growth metrics
- **Validation**: Email format, password strength, unique constraints

## Admin Product Management Features

Enhanced product management includes:

- **UUID Support**: All product IDs are now UUIDs
- **Enhanced Search**: Search by name, description, SKU, category
- **Advanced Filtering**: Price range, status, category filters
- **Stock Management**: Dedicated increase/decrease stock endpoints
- **Statistics**: Product analytics and overview
- **Validation**: Comprehensive input validation

## Important Notes

- All admin endpoints require authentication with a valid JWT token and ADMIN role
- Replace placeholder values (like UUIDs) with actual values from your database
- Some tests depend on data created by previous tests
- Always test with a development database, never production
- UUIDs are case-sensitive and must be valid UUID format

## Environment Variables

Make sure these are set in your `.env` file:

```
DATABASE_URL="postgresql://username:password@localhost:5432/shopie_dev"
JWT_SECRET="your-secret-key"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
```

## Testing Checklist

### User Management
- [ ] Create users with different roles
- [ ] Search and filter users
- [ ] Update user information
- [ ] Change user roles
- [ ] Delete users
- [ ] Test validation errors
- [ ] Test unauthorized access

### Product Management
- [ ] Create products with all fields
- [ ] Search and filter products
- [ ] Update product information
- [ ] Manage stock levels
- [ ] Delete products
- [ ] Test validation errors
- [ ] Test UUID format requirements

### Authentication & Authorization
- [ ] Admin login works
- [ ] JWT tokens are valid
- [ ] Admin-only endpoints reject customer tokens
- [ ] Endpoints reject invalid/expired tokens
- [ ] Role-based access control works

## Troubleshooting

### Common Issues

1. **Invalid UUID Format**: Ensure UUIDs are properly formatted (e.g., `123e4567-e89b-12d3-a456-************`)
2. **Token Expired**: Re-authenticate to get a fresh JWT token
3. **Database Connection**: Verify DATABASE_URL is correct and database is running
4. **Migration Issues**: Run `npx prisma migrate reset` to reset and re-apply migrations

### Getting UUIDs for Testing

After creating records, copy the UUIDs from the response and update your test variables:

```http
### Create a product and copy the returned UUID
POST {{baseUrl}}/admin/products
# Response will contain: { "data": { "id": "uuid-here", ... } }

### Then use the UUID in subsequent tests
@productId = uuid-from-previous-response
```
