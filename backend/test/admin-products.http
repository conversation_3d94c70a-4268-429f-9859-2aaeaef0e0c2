### Admin Product Management API Tests
### Base URL and Authentication

@baseUrl = http://localhost:3000/api
@adminEmail = <EMAIL>
@adminPassword = admin123

### Variables for testing
@productId = 1
@testSku = TEST-PROD-001

### 1. Admin Login (Get JWT Token)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{adminEmail}}",
  "password": "{{adminPassword}}"
}

### Extract token from response and set it as variable
### Copy the token from the response and use it in subsequent requests
@authToken = YOUR_JWT_TOKEN_HERE

### 2. Create New Product
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "Test Product",
  "shortDescription": "This is a test product for API testing",
  "description": "A comprehensive description of the test product with all the details that customers need to know about this amazing product.",
  "price": 99.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "images": [
    "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
    "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500"
  ],
  "sku": "{{testSku}}",
  "category": "Electronics",
  "stock": 50,
  "isActive": true
}

### 3. Get All Products (Admin View)
GET {{baseUrl}}/admin/products
Authorization: Bearer {{authToken}}

### 4. Get All Products with Pagination
GET {{baseUrl}}/admin/products?page=1&limit=5
Authorization: Bearer {{authToken}}

### 5. Search Products by Name
GET {{baseUrl}}/admin/products?search=Test
Authorization: Bearer {{authToken}}

### 6. Filter Products by Category
GET {{baseUrl}}/admin/products?category=Electronics
Authorization: Bearer {{authToken}}

### 7. Filter Products by Price Range
GET {{baseUrl}}/admin/products?minPrice=50&maxPrice=150
Authorization: Bearer {{authToken}}

### 8. Filter Active Products Only
GET {{baseUrl}}/admin/products?isActive=true
Authorization: Bearer {{authToken}}

### 9. Filter Inactive Products Only
GET {{baseUrl}}/admin/products?isActive=false
Authorization: Bearer {{authToken}}

### 10. Complex Filter (Search + Category + Price + Status)
GET {{baseUrl}}/admin/products?search=Test&category=Electronics&minPrice=50&maxPrice=200&isActive=true&page=1&limit=10
Authorization: Bearer {{authToken}}

### 11. Get Single Product by ID
GET {{baseUrl}}/admin/products/{{productId}}
Authorization: Bearer {{authToken}}

### 12. Update Product (Full Update)
PATCH {{baseUrl}}/admin/products/{{productId}}
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "Updated Test Product",
  "shortDescription": "This is an updated test product description",
  "description": "Updated comprehensive description with new features and improvements.",
  "price": 129.99,
  "imageUrl": "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500",
  "images": [
    "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500",
    "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
    "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500"
  ],
  "sku": "{{testSku}}-UPDATED",
  "category": "Electronics",
  "stock": 75,
  "isActive": true
}

### 13. Update Product (Partial Update - Price Only)
PATCH {{baseUrl}}/admin/products/{{productId}}
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "price": 149.99
}

### 14. Update Product (Partial Update - Stock Only)
PATCH {{baseUrl}}/admin/products/{{productId}}
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "stock": 100
}

### 15. Update Product (Partial Update - Status Only)
PATCH {{baseUrl}}/admin/products/{{productId}}
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "isActive": false
}

### 16. Increase Product Stock
PATCH {{baseUrl}}/admin/products/{{productId}}/stock/increase
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "quantity": 25
}

### 17. Decrease Product Stock
PATCH {{baseUrl}}/admin/products/{{productId}}/stock/decrease
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "quantity": 10
}

### 18. Get Product Statistics
GET {{baseUrl}}/admin/products/stats/overview
Authorization: Bearer {{authToken}}

### 19. Generate Sample Data
POST {{baseUrl}}/admin/products/generate-sample-data
Authorization: Bearer {{authToken}}

### 20. Delete Product
DELETE {{baseUrl}}/admin/products/{{productId}}
Authorization: Bearer {{authToken}}

### 21. Try to Access Admin Endpoint Without Token (Should Fail)
GET {{baseUrl}}/admin/products

### 22. Try to Access Admin Endpoint with Invalid Token (Should Fail)
GET {{baseUrl}}/admin/products
Authorization: Bearer invalid_token_here

### 23. Try to Create Product with Invalid Data (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "",
  "shortDescription": "",
  "price": -10,
  "imageUrl": "not-a-valid-url",
  "stock": -5
}

### 24. Try to Update Non-Existent Product (Should Fail)
PATCH {{baseUrl}}/admin/products/99999
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "This product doesn't exist"
}

### 25. Try to Delete Non-Existent Product (Should Fail)
DELETE {{baseUrl}}/admin/products/99999
Authorization: Bearer {{authToken}}

### 26. Create Product with Duplicate SKU (Should Fail)
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "Duplicate SKU Product",
  "shortDescription": "This product has a duplicate SKU",
  "price": 50.00,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "sku": "{{testSku}}",
  "category": "Test",
  "stock": 10,
  "isActive": true
}

### 27. Try to Decrease Stock Below Zero (Should Fail)
PATCH {{baseUrl}}/admin/products/{{productId}}/stock/decrease
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "quantity": 1000
}

### 28. Create Product with Minimum Required Fields
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "Minimal Product",
  "shortDescription": "Product with only required fields",
  "price": 25.00,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
}

### 29. Create Product with All Optional Fields
POST {{baseUrl}}/admin/products
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
  "name": "Complete Product",
  "shortDescription": "Product with all possible fields filled",
  "description": "This product has every single field filled out to test the complete data model.",
  "price": 199.99,
  "imageUrl": "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
  "images": [
    "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
    "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500",
    "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500"
  ],
  "sku": "COMPLETE-PROD-001",
  "category": "Premium Electronics",
  "stock": 25,
  "isActive": true
}

### 30. Test Sorting (if implemented)
GET {{baseUrl}}/admin/products?sortBy=price&sortOrder=desc
Authorization: Bearer {{authToken}}
