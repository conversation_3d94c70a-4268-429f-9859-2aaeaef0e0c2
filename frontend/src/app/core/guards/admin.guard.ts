import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (this.authService.isLoggedIn() && this.authService.isAdmin()) {
      return true;
    }

    if (this.authService.isLoggedIn()) {
      // User is logged in but not an admin
      this.router.navigate(['/']);
    } else {
      // User is not logged in
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: state.url }
      });
    }
    
    return false;
  }
}
