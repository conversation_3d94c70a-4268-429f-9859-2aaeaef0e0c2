<div class="notification-container">
  <div 
    *ngFor="let notification of notifications; trackBy: trackByFn" 
    [class]="getNotificationClass(notification.type)"
    [@slideIn]
  >
    <div class="notification-content">
      <div class="notification-icon">
        {{ getIconClass(notification.type) }}
      </div>
      <div class="notification-text">
        <div class="notification-title">{{ notification.title }}</div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
      <button 
        class="notification-close" 
        (click)="removeNotification(notification.id)"
        aria-label="Close notification"
      >
        ×
      </button>
    </div>
  </div>
</div>
