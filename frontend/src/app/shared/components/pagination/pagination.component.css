/* Pagination Component Styles */

/* Button hover effects */
button {
  transition: all 0.15s ease-in-out;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Active page button styles */
button.bg-blue-600 {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Disabled button styles */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Select dropdown styles */
select {
  padding: 0.375rem 0.75rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .flex-col {
    gap: 1rem;
  }
  
  .space-x-2 > * + * {
    margin-left: 0.5rem;
  }
  
  .space-y-4 > * + * {
    margin-top: 1rem;
  }
}

/* Focus styles for accessibility */
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Icon alignment */
svg {
  flex-shrink: 0;
}

/* Page number button group styling */
.space-x-1 > * + * {
  margin-left: 0.25rem;
}

/* Ensure consistent button heights */
button {
  min-height: 2.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Text color transitions */
.text-gray-500,
.text-gray-700 {
  transition: color 0.15s ease-in-out;
}

/* Border radius for button groups */
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

/* Ensure proper spacing in flex containers */
.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}
