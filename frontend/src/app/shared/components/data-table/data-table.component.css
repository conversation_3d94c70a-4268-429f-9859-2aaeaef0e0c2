/* Data Table Component Styles */

/* Table hover effects */
tbody tr:hover {
  background-color: #f9fafb;
  transition: background-color 0.15s ease-in-out;
}

/* Sortable column headers */
.cursor-pointer:hover {
  background-color: #f3f4f6;
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Custom scrollbar for table container */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Action button hover effects */
button {
  transition: all 0.15s ease-in-out;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Badge styles for different states */
.badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-danger {
  background-color: #fee2e2;
  color: #991b1b;
}

.badge-info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .py-4 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .text-sm {
    font-size: 0.75rem;
  }
}

/* Focus styles for accessibility */
input[type="checkbox"]:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Image placeholder styles */
img[src="assets/images/placeholder.png"] {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
}

/* Empty state styling */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: #6b7280;
}

/* Search input focus styles */
input[type="text"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Sort icon transitions */
svg {
  transition: color 0.15s ease-in-out;
}

/* Table cell content alignment */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}
