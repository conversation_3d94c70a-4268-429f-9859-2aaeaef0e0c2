<nav class="bg-white shadow">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <div class="flex">
        <div class="flex-shrink-0 flex items-center">
          <a routerLink="/" class="text-xl font-bold text-blue-600">Shopie</a>
        </div>
        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
          <!-- Desktop navigation -->
          <a routerLink="/" routerLinkActive="border-blue-500 text-gray-900" 
             [routerLinkActiveOptions]="{exact: true}"
             class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
            Home
          </a>
          <a routerLink="/products" routerLinkActive="border-blue-500 text-gray-900"
             class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
            Products
          </a>
          <a *ngIf="currentUser" routerLink="/cart" routerLinkActive="border-blue-500 text-gray-900"
             class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium relative">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
            </svg>
            Cart
            <span *ngIf="cartItemsCount > 0"
                  class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {{ cartItemsCount }}
            </span>
          </a>
          <a *ngIf="currentUser?.role === 'ADMIN'" routerLink="/admin" routerLinkActive="border-blue-500 text-gray-900"
             class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
            Admin
          </a>
        </div>
      </div>
      <div class="hidden sm:ml-6 sm:flex sm:items-center">
        <!-- Desktop profile dropdown -->
        <div *ngIf="currentUser; else authButtons" class="ml-3 relative">
          <div>
            <button type="button" (click)="toggleProfileDropdown()"
                    class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    id="user-menu-button" aria-expanded="false" aria-haspopup="true">
              <span class="sr-only">Open user menu</span>
              <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-semibold">
                {{ currentUser.firstName?.charAt(0) || currentUser.email.charAt(0).toUpperCase() }}
              </div>
            </button>
          </div>
          
          <div *ngIf="isProfileDropdownOpen"
               class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
               role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
            <a routerLink="/user/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
              Your Profile
            </a>
            <button (click)="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
              Sign out
            </button>
          </div>
        </div>
        
        <ng-template #authButtons>
          <div class="flex space-x-4">
            <a routerLink="/auth/login" 
               class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
              Sign in
            </a>
            <a routerLink="/auth/register" 
               class="bg-blue-600 text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
              Sign up
            </a>
          </div>
        </ng-template>
      </div>
      
      <div class="-mr-2 flex items-center sm:hidden">
        <!-- Mobile menu button -->
        <button type="button" (click)="toggleMenu()"
                class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                aria-controls="mobile-menu" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <!-- Icon when menu is closed -->
          <svg *ngIf="!isMenuOpen" class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <!-- Icon when menu is open -->
          <svg *ngIf="isMenuOpen" class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile menu, show/hide based on menu state -->
  <div *ngIf="isMenuOpen" class="sm:hidden" id="mobile-menu">
    <div class="pt-2 pb-3 space-y-1">
      <a routerLink="/" routerLinkActive="bg-blue-50 border-blue-500 text-blue-700" 
         [routerLinkActiveOptions]="{exact: true}"
         class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
        Home
      </a>
      <a routerLink="/products" routerLinkActive="bg-blue-50 border-blue-500 text-blue-700"
         class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
        Products
      </a>
      <a *ngIf="currentUser" routerLink="/cart" routerLinkActive="bg-blue-50 border-blue-500 text-blue-700"
         class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium relative">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
          </svg>
          Cart
          <span *ngIf="cartItemsCount > 0"
                class="ml-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {{ cartItemsCount }}
          </span>
        </div>
      </a>
      <a *ngIf="currentUser?.role === 'ADMIN'" routerLink="/admin" routerLinkActive="bg-blue-50 border-blue-500 text-blue-700"
         class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
        Admin
      </a>
    </div>
    
    <div *ngIf="currentUser; else mobileAuthButtons" class="pt-4 pb-3 border-t border-gray-200">
      <div class="flex items-center px-4">
        <div class="flex-shrink-0">
          <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-semibold">
            {{ currentUser.firstName?.charAt(0) || currentUser.email.charAt(0).toUpperCase() }}
          </div>
        </div>
        <div class="ml-3">
          <div class="text-base font-medium text-gray-800">
            {{ currentUser.firstName ? (currentUser.firstName + ' ' + (currentUser.lastName || '')) : currentUser.email }}
          </div>
          <div class="text-sm font-medium text-gray-500">{{ currentUser.email }}</div>
        </div>
      </div>
      <div class="mt-3 space-y-1">
        <a routerLink="/user/profile" 
           class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">
          Your Profile
        </a>
        <button (click)="logout()" 
                class="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">
          Sign out
        </button>
      </div>
    </div>
    
    <ng-template #mobileAuthButtons>
      <div class="pt-4 pb-3 border-t border-gray-200">
        <div class="flex items-center justify-center space-x-4 px-4">
          <a routerLink="/auth/login" 
             class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-base font-medium">
            Sign in
          </a>
          <a routerLink="/auth/register" 
             class="bg-blue-600 text-white hover:bg-blue-700 px-3 py-2 rounded-md text-base font-medium">
            Sign up
          </a>
        </div>
      </div>
    </ng-template>
  </div>
</nav>