<nav [ngClass]="{ 'bg-white/60 shadow-lg backdrop-blur-xl': scrolled, 'bg-white/30 backdrop-blur-md': !scrolled }"
     class="fixed top-0 left-0 w-full z-50 transition-all duration-300">
  <div class="max-w-7xl mx-auto flex items-center justify-between px-4 md:px-8 py-3">
    <a routerLink="/" class="text-2xl font-extrabold text-gray-800 tracking-tight">Shopie</a>
    <button class="md:hidden p-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-400"
            (click)="toggleMenu()" aria-label="Toggle navigation">
      <svg *ngIf="!menuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
           viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"/>
      </svg>
      <svg *ngIf="menuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
           viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M6 18L18 6M6 6l12 12"/>
      </svg>
    </button>
    <ul class="hidden md:flex space-x-8">
      <li><a routerLink="/home" routerLinkActive="text-blue-600"
             class="text-gray-700 hover:text-blue-600 font-medium transition">Home</a></li>
      <li><a routerLink="/products" routerLinkActive="text-blue-600"
             class="text-gray-700 hover:text-blue-600 font-medium transition">Products</a></li>
      <li><a routerLink="/cart" routerLinkActive="text-blue-600"
             class="text-gray-700 hover:text-blue-600 font-medium transition">Cart</a></li>
      <li><a routerLink="/profile" routerLinkActive="text-blue-600"
             class="text-gray-700 hover:text-blue-600 font-medium transition">Profile</a></li>
    </ul>
  </div>
  <!-- Mobile menu -->
  <div *ngIf="menuOpen" class="md:hidden bg-white/80 backdrop-blur-lg shadow-lg">
    <ul class="flex flex-col space-y-2 px-6 py-4">
      <li><a routerLink="/home" (click)="closeMenu()" class="block text-gray-700 hover:text-blue-600 font-medium">Home</a></li>
      <li><a routerLink="/products" (click)="closeMenu()" class="block text-gray-700 hover:text-blue-600 font-medium">Products</a></li>
      <li><a routerLink="/cart" (click)="closeMenu()" class="block text-gray-700 hover:text-blue-600 font-medium">Cart</a></li>
      <li><a routerLink="/profile" (click)="closeMenu()" class="block text-gray-700 hover:text-blue-600 font-medium">Profile</a></li>
    </ul>
  </div>
</nav>
