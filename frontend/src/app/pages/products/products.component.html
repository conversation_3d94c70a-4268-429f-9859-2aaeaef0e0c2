<div class="container mx-auto px-4 py-8">
  <!-- <PERSON> Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold mb-4">All Products</h1>
    <p class="text-gray-600">Discover our complete collection of amazing products</p>
  </div>

  <!-- Search and Filter Section -->
  <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Search Input -->
      <div class="md:col-span-2">
        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Products</label>
        <input
          id="search"
          type="text"
          [(ngModel)]="searchTerm"
          (keyup.enter)="onSearch()"
          placeholder="Search by name or description..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
      </div>

      <!-- Price Range -->
      <div>
        <label for="minPrice" class="block text-sm font-medium text-gray-700 mb-2">Min Price</label>
        <input
          id="minPrice"
          type="number"
          [(ngModel)]="minPrice"
          (change)="onFilterChange()"
          placeholder="$0"
          min="0"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
      </div>

      <div>
        <label for="maxPrice" class="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
        <input
          id="maxPrice"
          type="number"
          [(ngModel)]="maxPrice"
          (change)="onFilterChange()"
          placeholder="$999"
          min="0"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-4 mt-4">
      <button
        (click)="onSearch()"
        class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        Search
      </button>
      <button
        (click)="clearFilters()"
        class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
      >
        Clear Filters
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-12">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <p class="mt-4 text-gray-600">Loading products...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
    <div class="flex items-center">
      <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      {{ error }}
    </div>
  </div>

  <!-- Products Results Info -->
  <div *ngIf="!isLoading && !error" class="mb-6">
    <p class="text-gray-600">
      Showing {{ products.length }} of {{ totalProducts }} products
      <span *ngIf="searchTerm || minPrice || maxPrice"> (filtered)</span>
    </p>
  </div>

  <!-- Products Grid -->
  <div *ngIf="!isLoading && !error && products.length > 0" class="mb-8">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      <div *ngFor="let product of products" class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <!-- Product Image -->
        <div class="relative">
          <img
            [src]="product.imageUrl || 'assets/images/placeholder.png'"
            [alt]="product.name"
            class="w-full h-48 object-cover"
            (error)="onImageError($event)"
          >
          <!-- Stock Badge -->
          <div class="absolute top-2 right-2">
            <span
              [class]="product.stock > 0 ? 'bg-green-500' : 'bg-red-500'"
              class="text-white text-xs px-2 py-1 rounded-full"
            >
              {{ product.stock > 0 ? 'In Stock' : 'Out of Stock' }}
            </span>
          </div>
        </div>

        <!-- Product Info -->
        <div class="p-4">
          <h3 class="font-semibold text-lg mb-2 line-clamp-1" [title]="product.name">
            {{ product.name }}
          </h3>
          <p class="text-gray-600 mb-3 line-clamp-2 text-sm" [title]="product.shortDescription">
            {{ product.shortDescription }}
          </p>

          <!-- Price and Stock Info -->
          <div class="flex justify-between items-center mb-3">
            <span class="font-bold text-xl text-blue-600">
              ${{ product.price.toFixed(2) }}
            </span>
            <span class="text-sm text-gray-500">
              {{ product.stock }} left
            </span>
          </div>

          <!-- Action Button -->
          <div class="flex gap-2">
            <a
              [routerLink]="['/products', product.id]"
              class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
            >
              View Details
            </a>
            <button
              *ngIf="product.stock > 0"
              (click)="addToCart(product)"
              [disabled]="addingToCartProductId === product.id"
              class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 transition-colors duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              [title]="isAuthenticated ? 'Add to Cart' : 'Sign in to Add to Cart'"
            >
              <svg *ngIf="addingToCartProductId !== product.id" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
              </svg>
              <svg *ngIf="addingToCartProductId === product.id" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && products.length === 0" class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
    <p class="text-gray-500 mb-4">
      <span *ngIf="searchTerm || minPrice || maxPrice">
        Try adjusting your search criteria or clearing the filters.
      </span>
      <span *ngIf="!searchTerm && !minPrice && !maxPrice">
        No products are available at the moment.
      </span>
    </p>
    <button
      *ngIf="searchTerm || minPrice || maxPrice"
      (click)="clearFilters()"
      class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
    >
      Clear Filters
    </button>
  </div>

  <!-- Pagination -->
  <div *ngIf="!isLoading && !error && totalPages > 1" class="flex justify-center items-center space-x-2 mt-8">
    <!-- Previous Button -->
    <button
      (click)="prevPage()"
      [disabled]="!hasPrev"
      [class]="!hasPrev ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'"
      class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium"
    >
      Previous
    </button>

    <!-- Page Numbers -->
    <div class="flex space-x-1">
      <!-- First page -->
      <button
        *ngIf="currentPage > 3"
        (click)="goToPage(1)"
        class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium bg-white text-gray-700 hover:bg-gray-50"
      >
        1
      </button>

      <!-- Ellipsis -->
      <span *ngIf="currentPage > 4" class="px-3 py-2 text-gray-500">...</span>

      <!-- Pages around current page -->
      <button
        *ngFor="let page of [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2]"
        [hidden]="page < 1 || page > totalPages"
        (click)="goToPage(page)"
        [class]="page === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
        class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium"
      >
        {{ page }}
      </button>

      <!-- Ellipsis -->
      <span *ngIf="currentPage < totalPages - 3" class="px-3 py-2 text-gray-500">...</span>

      <!-- Last page -->
      <button
        *ngIf="currentPage < totalPages - 2"
        (click)="goToPage(totalPages)"
        class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium bg-white text-gray-700 hover:bg-gray-50"
      >
        {{ totalPages }}
      </button>
    </div>

    <!-- Next Button -->
    <button
      (click)="nextPage()"
      [disabled]="!hasNext"
      [class]="!hasNext ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'"
      class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium"
    >
      Next
    </button>
  </div>

  <!-- Page Info -->
  <div *ngIf="!isLoading && !error && totalPages > 1" class="text-center mt-4">
    <p class="text-sm text-gray-600">
      Page {{ currentPage }} of {{ totalPages }} ({{ totalProducts }} total products)
    </p>
  </div>
</div>
