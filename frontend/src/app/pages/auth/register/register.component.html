<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Create your account</h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Or
        <a routerLink="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
          sign in to your account
        </a>
      </p>
    </div>
    
    <form class="mt-8 space-y-6" [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm -space-y-px">
        <div class="grid grid-cols-1 gap-y-2">
          <div>
            <label for="email" class="sr-only">Email address</label>
            <input id="email" name="email" type="email" formControlName="email" required
                   class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                   placeholder="Email address">
            <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched" 
                 class="text-red-500 text-xs mt-1">
              Please enter a valid email address
            </div>
          </div>
          
          <div>
            <label for="password" class="sr-only">Password</label>
            <input id="password" name="password" type="password" formControlName="password" required
                   class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                   placeholder="Password">
            <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched" 
                 class="text-red-500 text-xs mt-1">
              Password must be at least 6 characters
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-x-3">
            <div>
              <label for="firstName" class="sr-only">First Name</label>
              <input id="firstName" name="firstName" type="text" formControlName="firstName"
                     class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                     placeholder="First Name">
            </div>
            
            <div>
              <label for="lastName" class="sr-only">Last Name</label>
              <input id="lastName" name="lastName" type="text" formControlName="lastName"
                     class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                     placeholder="Last Name">
            </div>
          </div>
          
          <div>
            <label for="phone" class="sr-only">Phone</label>
            <input id="phone" name="phone" type="text" formControlName="phone"
                   class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                   placeholder="Phone Number (optional)">
          </div>
        </div>
      </div>

      <div *ngIf="errorMessage" class="text-red-500 text-sm text-center">
        {{ errorMessage }}
      </div>

      <div>
        <button type="submit" [disabled]="registerForm.invalid || isSubmitting"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300">
          <span *ngIf="isSubmitting" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <!-- Loading spinner -->
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Sign up
        </button>
      </div>
    </form>
  </div>
</div>
