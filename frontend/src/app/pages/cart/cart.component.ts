import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CartService, Cart, CartItem } from '../../core/services/cart.service';

@Component({
  selector: 'app-cart',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './cart.component.html',
  styleUrl: './cart.component.css'
})
export class CartComponent implements OnInit {
  cart: Cart | null = null;
  isLoading = true;
  error = '';
  isUpdating = false;
  updatingItemId: number | null = null;

  constructor(private cartService: CartService) {}

  ngOnInit(): void {
    this.loadCart();
  }

  loadCart(): void {
    this.isLoading = true;
    this.cartService.getCart().subscribe({
      next: (response) => {
        if (response.success) {
          this.cart = response.data;
        } else {
          this.error = 'Failed to load cart';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load cart';
        this.isLoading = false;
        console.error('Error loading cart:', error);
      }
    });
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/placeholder.png';
  }

  updateQuantity(item: CartItem, newQuantity: number): void {
    if (newQuantity < 1 || newQuantity > item.product.stock) {
      return;
    }

    this.updatingItemId = item.id;
    this.cartService.updateCartItem(item.id, newQuantity).subscribe({
      next: (response) => {
        if (response.success) {
          this.cart = response.data;
        }
        this.updatingItemId = null;
      },
      error: (error) => {
        console.error('Error updating cart item:', error);
        this.updatingItemId = null;
      }
    });
  }

  increaseQuantity(item: CartItem): void {
    this.updateQuantity(item, item.quantity + 1);
  }

  decreaseQuantity(item: CartItem): void {
    this.updateQuantity(item, item.quantity - 1);
  }

  removeItem(item: CartItem): void {
    if (confirm('Are you sure you want to remove this item from your cart?')) {
      this.updatingItemId = item.id;
      this.cartService.removeFromCart(item.id).subscribe({
        next: (response) => {
          if (response.success) {
            this.cart = response.data;
          }
          this.updatingItemId = null;
        },
        error: (error) => {
          console.error('Error removing cart item:', error);
          this.updatingItemId = null;
        }
      });
    }
  }

  clearCart(): void {
    if (confirm('Are you sure you want to clear your entire cart?')) {
      this.isUpdating = true;
      this.cartService.clearCart().subscribe({
        next: () => {
          this.loadCart(); // Reload cart to get updated state
          this.isUpdating = false;
        },
        error: (error) => {
          console.error('Error clearing cart:', error);
          this.isUpdating = false;
        }
      });
    }
  }

  getItemSubtotal(item: CartItem): number {
    return item.product.price * item.quantity;
  }

  getTotalPrice(): number {
    if (!this.cart || !this.cart.items) {
      return 0;
    }
    return this.cart.items.reduce((total, item) => total + this.getItemSubtotal(item), 0);
  }

  getTotalItems(): number {
    if (!this.cart || !this.cart.items) {
      return 0;
    }
    return this.cart.items.reduce((total, item) => total + item.quantity, 0);
  }
}
