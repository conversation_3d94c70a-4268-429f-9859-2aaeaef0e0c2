<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

    <!-- <PERSON> Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
      <p class="text-gray-600 mt-2">Review your items and proceed to checkout</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center min-h-96">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !isLoading" class="text-center py-12">
      <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
        <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="text-lg font-medium text-red-800 mb-2">Error</h3>
        <p class="text-red-600">{{ error }}</p>
        <button (click)="loadCart()"
                class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          Try Again
        </button>
      </div>
    </div>

    <!-- Empty Cart -->
    <div *ngIf="!isLoading && !error && cart && cart.items.length === 0" class="text-center py-12">
      <div class="bg-white rounded-lg shadow-sm p-8 max-w-md mx-auto">
        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
        </svg>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Your cart is empty</h3>
        <p class="text-gray-600 mb-6">Start shopping to add items to your cart</p>
        <a routerLink="/products"
           class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
          </svg>
          Start Shopping
        </a>
      </div>
    </div>

    <!-- Cart Content -->
    <div *ngIf="!isLoading && !error && cart && cart.items.length > 0" class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Cart Items -->
      <div class="lg:col-span-2 space-y-4">
        <div class="bg-white rounded-lg shadow-sm">

          <!-- Cart Header -->
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-900">
              Cart Items ({{ getTotalItems() }})
            </h2>
            <button
              (click)="clearCart()"
              [disabled]="isUpdating"
              class="text-red-600 hover:text-red-700 text-sm font-medium disabled:opacity-50"
            >
              Clear Cart
            </button>
          </div>

          <!-- Cart Items List -->
          <div class="divide-y divide-gray-200">
            <div *ngFor="let item of cart.items" class="p-6">
              <div class="flex items-start space-x-4">

                <!-- Product Image -->
                <div class="flex-shrink-0">
                  <img
                    [src]="item.product.imageUrl || 'assets/images/placeholder.png'"
                    [alt]="item.product.name"
                    class="w-20 h-20 object-cover rounded-lg"
                    (error)="onImageError($event)"
                  >
                </div>

                <!-- Product Details -->
                <div class="flex-1 min-w-0">
                  <div class="flex justify-between items-start">
                    <div class="flex-1">
                      <h3 class="text-lg font-medium text-gray-900 mb-1">
                        <a [routerLink]="['/products', item.product.id]"
                           class="hover:text-blue-600 transition-colors">
                          {{ item.product.name }}
                        </a>
                      </h3>
                      <p class="text-gray-600 text-sm mb-2">{{ item.product.shortDescription }}</p>
                      <p class="text-lg font-semibold text-blue-600">${{ item.product.price.toFixed(2) }}</p>
                    </div>

                    <!-- Remove Button -->
                    <button
                      (click)="removeItem(item)"
                      [disabled]="updatingItemId === item.id"
                      class="text-red-500 hover:text-red-700 p-1 disabled:opacity-50"
                      title="Remove item"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>

                  <!-- Quantity Controls -->
                  <div class="flex items-center space-x-3 mt-4">
                    <span class="text-sm text-gray-700 font-medium">Quantity:</span>

                    <div class="flex items-center space-x-2">
                      <button
                        (click)="decreaseQuantity(item)"
                        [disabled]="item.quantity <= 1 || updatingItemId === item.id"
                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                        </svg>
                      </button>

                      <span class="w-12 text-center font-medium">
                        {{ updatingItemId === item.id ? '...' : item.quantity }}
                      </span>

                      <button
                        (click)="increaseQuantity(item)"
                        [disabled]="item.quantity >= item.product.stock || updatingItemId === item.id"
                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                      </button>
                    </div>

                    <span class="text-sm text-gray-500">
                      ({{ item.product.stock }} available)
                    </span>
                  </div>

                  <!-- Item Subtotal -->
                  <div class="mt-3 flex justify-between items-center">
                    <span class="text-sm text-gray-600">Subtotal:</span>
                    <span class="text-lg font-semibold text-gray-900">
                      ${{ getItemSubtotal(item).toFixed(2) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cart Summary -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm p-6 sticky top-8">

          <!-- Summary Header -->
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

          <!-- Summary Details -->
          <div class="space-y-3 mb-6">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Items ({{ getTotalItems() }}):</span>
              <span class="font-medium">${{ getTotalPrice().toFixed(2) }}</span>
            </div>

            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Shipping:</span>
              <span class="font-medium text-green-600">Free</span>
            </div>

            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Tax:</span>
              <span class="font-medium">Calculated at checkout</span>
            </div>

            <div class="border-t pt-3">
              <div class="flex justify-between">
                <span class="text-lg font-semibold text-gray-900">Total:</span>
                <span class="text-xl font-bold text-blue-600">${{ getTotalPrice().toFixed(2) }}</span>
              </div>
            </div>
          </div>

          <!-- Checkout Button -->
          <button
            class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors mb-4"
            disabled
          >
            Proceed to Checkout
            <span class="block text-xs opacity-75 mt-1">(Coming Soon)</span>
          </button>

          <!-- Continue Shopping -->
          <a
            routerLink="/products"
            class="block w-full text-center bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
          >
            Continue Shopping
          </a>

          <!-- Security Badge -->
          <div class="mt-6 pt-6 border-t">
            <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
              <span>Secure checkout</span>
            </div>
          </div>

          <!-- Estimated Delivery -->
          <div class="mt-4 p-3 bg-green-50 rounded-lg">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-sm text-green-800 font-medium">Free shipping on orders over $50</span>
            </div>
            <p class="text-xs text-green-600 mt-1">Estimated delivery: 3-5 business days</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
