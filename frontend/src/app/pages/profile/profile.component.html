<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h1 class="text-2xl font-bold text-gray-900">Profile Settings</h1>
        <p class="mt-1 text-sm text-gray-600">Manage your account settings and preferences</p>
      </div>

      <!-- Tab Navigation -->
      <div class="px-6">
        <nav class="flex space-x-8" aria-label="Tabs">
          <button
            (click)="setActiveTab('profile')"
            [class]="activeTab === 'profile' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
          >
            Profile Information
          </button>
          <button
            (click)="setActiveTab('password')"
            [class]="activeTab === 'password' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
          >
            Change Password
          </button>
        </nav>
      </div>
    </div>

    <!-- Profile Information Tab -->
    <div *ngIf="activeTab === 'profile'" class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Profile Information</h2>
        <p class="mt-1 text-sm text-gray-600">Update your personal information</p>
      </div>

      <form [formGroup]="profileForm" (ngSubmit)="onUpdateProfile()" class="px-6 py-4 space-y-6">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <!-- First Name -->
          <div>
            <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
            <input
              type="text"
              id="firstName"
              formControlName="firstName"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              [class.border-red-500]="getFieldError(profileForm, 'firstName')"
            >
            <p *ngIf="getFieldError(profileForm, 'firstName')" class="mt-1 text-sm text-red-600">
              {{ getFieldError(profileForm, 'firstName') }}
            </p>
          </div>

          <!-- Last Name -->
          <div>
            <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
            <input
              type="text"
              id="lastName"
              formControlName="lastName"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              [class.border-red-500]="getFieldError(profileForm, 'lastName')"
            >
            <p *ngIf="getFieldError(profileForm, 'lastName')" class="mt-1 text-sm text-red-600">
              {{ getFieldError(profileForm, 'lastName') }}
            </p>
          </div>
        </div>

        <!-- Email (disabled) -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
          <input
            type="email"
            id="email"
            formControlName="email"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 sm:text-sm"
            readonly
          >
          <p class="mt-1 text-sm text-gray-500">Email address cannot be changed</p>
        </div>

        <!-- Phone -->
        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
          <input
            type="tel"
            id="phone"
            formControlName="phone"
            placeholder="+****************"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            [class.border-red-500]="getFieldError(profileForm, 'phone')"
          >
          <p *ngIf="getFieldError(profileForm, 'phone')" class="mt-1 text-sm text-red-600">
            {{ getFieldError(profileForm, 'phone') }}
          </p>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            [disabled]="profileForm.invalid || isUpdatingProfile"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span *ngIf="isUpdatingProfile" class="mr-2">
              <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ isUpdatingProfile ? 'Updating...' : 'Update Profile' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Change Password Tab -->
    <div *ngIf="activeTab === 'password'" class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Change Password</h2>
        <p class="mt-1 text-sm text-gray-600">Update your password to keep your account secure</p>
      </div>

      <form [formGroup]="passwordForm" (ngSubmit)="onChangePassword()" class="px-6 py-4 space-y-6">
        <!-- Current Password -->
        <div>
          <label for="currentPassword" class="block text-sm font-medium text-gray-700">Current Password</label>
          <input
            type="password"
            id="currentPassword"
            formControlName="currentPassword"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            [class.border-red-500]="getFieldError(passwordForm, 'currentPassword')"
          >
          <p *ngIf="getFieldError(passwordForm, 'currentPassword')" class="mt-1 text-sm text-red-600">
            {{ getFieldError(passwordForm, 'currentPassword') }}
          </p>
        </div>

        <!-- New Password -->
        <div>
          <label for="newPassword" class="block text-sm font-medium text-gray-700">New Password</label>
          <input
            type="password"
            id="newPassword"
            formControlName="newPassword"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            [class.border-red-500]="getFieldError(passwordForm, 'newPassword')"
          >
          <p *ngIf="getFieldError(passwordForm, 'newPassword')" class="mt-1 text-sm text-red-600">
            {{ getFieldError(passwordForm, 'newPassword') }}
          </p>
        </div>

        <!-- Confirm Password -->
        <div>
          <label for="confirmPassword" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
          <input
            type="password"
            id="confirmPassword"
            formControlName="confirmPassword"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            [class.border-red-500]="getFieldError(passwordForm, 'confirmPassword') || getPasswordFormError()"
          >
          <p *ngIf="getFieldError(passwordForm, 'confirmPassword')" class="mt-1 text-sm text-red-600">
            {{ getFieldError(passwordForm, 'confirmPassword') }}
          </p>
          <p *ngIf="getPasswordFormError()" class="mt-1 text-sm text-red-600">
            {{ getPasswordFormError() }}
          </p>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            [disabled]="passwordForm.invalid || isChangingPassword"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span *ngIf="isChangingPassword" class="mr-2">
              <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ isChangingPassword ? 'Changing...' : 'Change Password' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
