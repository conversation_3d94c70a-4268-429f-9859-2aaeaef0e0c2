<!-- <PERSON> -->
<div class="mb-8">
  <h1 class="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
  <p class="mt-2 text-sm text-gray-700">Welcome to the admin dashboard. Here's an overview of your store.</p>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="flex items-center justify-center py-12">
  <div class="inline-flex items-center">
    <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    <span class="text-lg text-gray-600">Loading dashboard...</span>
  </div>
</div>

<!-- Error State -->
<div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
  <div class="flex">
    <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <div class="ml-3">
      <p class="text-sm text-red-800">{{ error }}</p>
      <button 
        (click)="loadStats()" 
        class="mt-2 text-sm text-red-600 hover:text-red-500 underline"
      >
        Try again
      </button>
    </div>
  </div>
</div>

<!-- Dashboard Content -->
<div *ngIf="stats && !loading" class="space-y-8">
  <!-- Key Metrics -->
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Total Products -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10"></path>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
              <dd class="text-lg font-medium text-gray-900">{{ stats.total }}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Active Products -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Active Products</dt>
              <dd class="text-lg font-medium text-gray-900">{{ stats.active }}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- In Stock -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1"></path>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">In Stock</dt>
              <dd [class]="'text-lg font-medium ' + getStockStatusColor(stats.inStock, stats.total)">
                {{ stats.inStock }} / {{ stats.total }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Value -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Value</dt>
              <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(stats.totalValue) }}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Categories Overview -->
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Products by Category</h3>
      
      <div *ngIf="getCategoryEntries().length === 0" class="text-center py-8 text-gray-500">
        No categories found. Create some products to see category distribution.
      </div>
      
      <div *ngIf="getCategoryEntries().length > 0" class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <div *ngFor="let category of getCategoryEntries()" class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900">{{ category.key || 'Uncategorized' }}</p>
              <p class="text-2xl font-bold text-gray-700">{{ category.value }}</p>
            </div>
            <div class="text-sm text-gray-500">
              {{ ((category.value / stats.total) * 100).toFixed(1) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
      
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Add Product -->
        <a 
          routerLink="/admin/products" 
          class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
        >
          <div>
            <span class="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
            </span>
          </div>
          <div class="mt-4">
            <h3 class="text-lg font-medium">
              <span class="absolute inset-0" aria-hidden="true"></span>
              Add New Product
            </h3>
            <p class="mt-2 text-sm text-gray-500">
              Create a new product in your catalog
            </p>
          </div>
        </a>

        <!-- Manage Products -->
        <a 
          routerLink="/admin/products" 
          class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
        >
          <div>
            <span class="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </span>
          </div>
          <div class="mt-4">
            <h3 class="text-lg font-medium">
              <span class="absolute inset-0" aria-hidden="true"></span>
              Manage Products
            </h3>
            <p class="mt-2 text-sm text-gray-500">
              View, edit, and organize your products
            </p>
          </div>
        </a>

        <!-- View Reports -->
        <div class="group relative bg-white p-6 rounded-lg border border-gray-200 opacity-50 cursor-not-allowed">
          <div>
            <span class="rounded-lg inline-flex p-3 bg-gray-50 text-gray-400 ring-4 ring-white">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </span>
          </div>
          <div class="mt-4">
            <h3 class="text-lg font-medium text-gray-400">
              View Reports
            </h3>
            <p class="mt-2 text-sm text-gray-400">
              Coming soon - Analytics and reports
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity Placeholder -->
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
      <div class="text-center py-8 text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <p class="mt-2">Activity tracking coming soon</p>
      </div>
    </div>
  </div>
</div>
