<div class="min-h-screen bg-gray-50">
  <!-- Mobile sidebar backdrop -->
  <div
    *ngIf="sidebarOpen"
    class="fixed inset-0 z-40 lg:hidden"
    (click)="closeSidebar()"
  >
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
  </div>

  <!-- Sidebar -->
  <div
    class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
    [class.translate-x-0]="sidebarOpen"
    [class.-translate-x-full]="!sidebarOpen"
  >
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-6 bg-blue-600">
      <div class="flex items-center">
        <h1 class="text-xl font-bold text-white">Admin Panel</h1>
      </div>
      <button
        (click)="closeSidebar()"
        class="lg:hidden text-white hover:text-gray-200"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Navigation -->
    <nav class="mt-8">
      <div class="px-4 space-y-2">
        <a
          *ngFor="let item of navigationItems"
          (click)="navigateTo(item.route)"
          [class]="'flex items-center px-4 py-3 text-sm font-medium rounded-lg cursor-pointer transition-colors duration-200 ' +
                   (item.active ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-100')"
        >
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon"></path>
          </svg>
          {{ item.label }}
        </a>
      </div>
    </nav>

    <!-- User info at bottom -->
    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-white">
              {{ currentUser?.email?.charAt(0).toUpperCase() }}
            </span>
          </div>
        </div>
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium text-gray-700 truncate">{{ currentUser?.email }}</p>
          <p class="text-xs text-gray-500">Administrator</p>
        </div>
        <button
          (click)="logout()"
          class="ml-2 text-gray-400 hover:text-gray-600"
          title="Logout"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Main content -->
  <div class="lg:pl-64">
    <!-- Top header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        <!-- Mobile menu button -->
        <button
          (click)="toggleSidebar()"
          class="lg:hidden text-gray-500 hover:text-gray-700"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>

        <!-- Breadcrumbs -->
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-2">
            <li *ngFor="let crumb of breadcrumbs; let last = last" class="flex items-center">
              <a
                *ngIf="!last && crumb.route"
                [routerLink]="crumb.route"
                class="text-gray-500 hover:text-gray-700 text-sm font-medium"
              >
                {{ crumb.label }}
              </a>
              <span
                *ngIf="last"
                class="text-gray-900 text-sm font-medium"
              >
                {{ crumb.label }}
              </span>
              <svg
                *ngIf="!last"
                class="w-4 h-4 mx-2 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </li>
          </ol>
        </nav>

        <!-- Header actions -->
        <div class="flex items-center space-x-4">
          <!-- Notifications (placeholder) -->
          <button class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Page content -->
    <main class="p-4 sm:p-6 lg:p-8">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
