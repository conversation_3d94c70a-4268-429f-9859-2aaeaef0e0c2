<div class="container mx-auto px-4 py-8">
  <!-- Hero Section -->
  <div class="hero-gradient text-white rounded-lg p-8 mb-8 hero-section">
    <h1 class="text-4xl font-bold mb-4">Welcome to Shopie</h1>
    <p class="text-xl mb-6">Discover amazing products at great prices</p>
    <a routerLink="/products" class="bg-white text-blue-600 px-6 py-2 rounded-md font-medium btn-hero">
      Shop Now
    </a>
  </div>

  <!-- Loading and Error States -->
  <div *ngIf="isLoading" class="text-center py-8">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
    <p class="loading-pulse">Loading products...</p>
  </div>
  
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <!-- Featured Products Section -->
  <div *ngIf="!isLoading && !error && featuredProducts.length > 0" class="mb-12">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold section-header">Featured Products</h2>
      <a routerLink="/products" class="text-blue-600 hover:underline">View All</a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      <div *ngFor="let product of featuredProducts" class="border rounded-lg overflow-hidden shadow-sm product-card">
        <img
          [src]="product.imageUrl || 'assets/images/placeholder.png'"
          [alt]="product.name"
          class="w-full h-48 object-cover product-image"
          (error)="onImageError($event)"
        >
        <div class="p-4">
          <h3 class="font-semibold text-lg mb-2 line-clamp-1">{{ product.name }}</h3>
          <p class="text-gray-600 mb-2 line-clamp-2">{{ product.shortDescription }}</p>
          <div class="flex justify-between items-center mb-3">
            <span class="font-bold text-blue-600">${{ product.price.toFixed(2) }}</span>
            <span class="text-sm text-gray-500">{{ product.stock }} left</span>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-2">
            <a
              [routerLink]="['/products', product.id]"
              class="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              View Details
            </a>
            <button
              *ngIf="product.stock > 0"
              (click)="addToCart(product)"
              [disabled]="addingToCartProductId === product.id"
              class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              [title]="isAuthenticated ? 'Add to Cart' : 'Sign in to Add to Cart'"
            >
              <svg *ngIf="addingToCartProductId !== product.id" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
              </svg>
              <svg *ngIf="addingToCartProductId === product.id" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- New Arrivals Section -->
  <div *ngIf="!isLoading && !error && newArrivals.length > 0" class="mb-12">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold section-header">New Arrivals</h2>
      <a routerLink="/products" class="text-blue-600 hover:underline">View All</a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      <div *ngFor="let product of newArrivals" class="border rounded-lg overflow-hidden shadow-sm product-card">
        <img
          [src]="product.imageUrl || 'assets/images/placeholder.png'"
          [alt]="product.name"
          class="w-full h-48 object-cover product-image"
          (error)="onImageError($event)"
        >
        <div class="p-4">
          <h3 class="font-semibold text-lg mb-2 line-clamp-1">{{ product.name }}</h3>
          <p class="text-gray-600 mb-2 line-clamp-2">{{ product.shortDescription }}</p>
          <div class="flex justify-between items-center mb-3">
            <span class="font-bold text-blue-600">${{ product.price.toFixed(2) }}</span>
            <span class="text-sm text-gray-500">{{ product.stock }} left</span>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-2">
            <a
              [routerLink]="['/products', product.id]"
              class="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              View Details
            </a>
            <button
              *ngIf="product.stock > 0"
              (click)="addToCart(product)"
              [disabled]="addingToCartProductId === product.id"
              class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              [title]="isAuthenticated ? 'Add to Cart' : 'Sign in to Add to Cart'"
            >
              <svg *ngIf="addingToCartProductId !== product.id" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
              </svg>
              <svg *ngIf="addingToCartProductId === product.id" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
